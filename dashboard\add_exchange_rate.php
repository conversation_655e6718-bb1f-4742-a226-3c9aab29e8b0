<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/office_manager.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('offices.rates.create');

$officeMgr = new OfficeManager(new Database());
$errors = [];

// Get office ID
$officeId = (int)($_GET['office_id'] ?? 0);
if (!$officeId) {
    set_flash('danger', 'معرف المكتب غير صحيح');
    redirect('offices.php');
}

// Get office details
$office = $officeMgr->getOfficeById($officeId);
if (!$office) {
    set_flash('danger', 'المكتب غير موجود');
    redirect('offices.php');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf = $_POST['csrf_token'] ?? '';
    if (!verify_csrf_token($csrf)) {
        $errors[] = 'CSRF غير صالح';
    } else {
        // Validate and sanitize input
        $operationName = trim($_POST['operation_name'] ?? '');
        $exchangeRatePercentage = (float)($_POST['exchange_rate_percentage'] ?? 0);
        $isActive = isset($_POST['is_active']) ? 1 : 0;

        // Validation
        if (empty($operationName)) {
            $errors[] = 'اسم العملية مطلوب';
        } elseif (strlen($operationName) < 2) {
            $errors[] = 'اسم العملية يجب أن يكون حرفين على الأقل';
        }

        if ($exchangeRatePercentage <= 0) {
            $errors[] = 'سعر القص يجب أن يكون أكبر من صفر';
        }

        // Check if operation name already exists for this office
        if (empty($errors) && $officeMgr->operationExistsForOffice($officeId, $operationName)) {
            $errors[] = 'اسم العملية موجود مسبقاً لهذا المكتب';
        }

        if (empty($errors)) {
            $rateData = [
                'office_id' => $officeId,
                'operation_name' => $operationName,
                'exchange_rate_percentage' => $exchangeRatePercentage,
                'is_active' => $isActive
            ];

            $newRateId = $officeMgr->addExchangeRate($rateData);
            if ($newRateId) {
                log_activity($auth->getCurrentUser()['id'], 'office.rate.create', [
                    'rate_id' => $newRateId,
                    'office_id' => $officeId
                ]);
                set_flash('success', 'تم إضافة سعر القص بنجاح');
                redirect("office_details.php?id=$officeId");
            } else {
                $errors[] = 'حدث خطأ أثناء حفظ سعر القص';
            }
        }
    }
}

$csrf = get_csrf_token();
$pageTitle = 'إضافة سعر قص جديد - ' . $office['office_name'];
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>

<div class="container p-3 p-md-4">
    <div class="row">
        <div class="col-12 col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-plus"></i> إضافة سعر قص جديد
                    </h4>
                    <small class="text-muted">المكتب: <?php echo htmlspecialchars($office['office_name']); ?></small>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="post" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">
                        
                        <div class="row">
                            <div class="col-12 col-md-8 mb-3">
                                <label for="operation_name" class="form-label">اسم العملية <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="operation_name" name="operation_name" 
                                       value="<?php echo htmlspecialchars($_POST['operation_name'] ?? ''); ?>" 
                                       placeholder="مثال: الإمارات العربية المتحدة، مصر، الأردن" required>
                                <div class="form-text">أدخل اسم العملية أو البلد</div>
                                <div class="invalid-feedback">
                                    اسم العملية مطلوب
                                </div>
                            </div>
                            
                            <div class="col-12 col-md-4 mb-3">
                                <label for="exchange_rate_percentage" class="form-label">سعر القص <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="exchange_rate_percentage"
                                       name="exchange_rate_percentage"
                                       value="<?php echo htmlspecialchars($_POST['exchange_rate_percentage'] ?? ''); ?>"
                                       step="any" 
                                       placeholder="0.00" required>
                                <div class="form-text">مثال: 0.02</div>
                                <div class="invalid-feedback">
                                    سعر القص مطلوب
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch d-flex flex-row-reverse align-items-center gap-2" style="min-height: 38px;">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       <?php echo isset($_POST['is_active']) ? 'checked' : ''; ?>>
                                <label class="form-check-label mb-0" for="is_active" style="user-select:none;">
                                    سعر القص نشط
                                </label>
                            </div>
                        </div>

                        <!-- Calculation Preview -->
                        <div class="alert alert-info">
                            <h6 class="mb-2"><i class="fas fa-calculator"></i> معاينة الحساب</h6>
                            <div class="row">
                                <div class="col-12">
                                    <p class="mb-1">إذا كان المبلغ 1000 وسعر القص 0.02:</p>
                                    <p class="mb-0"><strong>سعر القص = 1000 × 0.02 = 20</strong></p>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex flex-column flex-sm-row justify-content-between gap-2">
                            <a href="office_details.php?id=<?php echo $office['id']; ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> رجوع
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ سعر القص
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?> 