<?php
/**
 * Export Office Operations
 * Exports office operations to PDF or Excel
 * Updated: Header layout with Arabic (right), Logo (center), English (left)
 */
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/office_manager.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('offices.operations.view');

$officeMgr = new OfficeManager(new Database());

// Get office ID
$officeId = (int)($_GET['office_id'] ?? 0);
if (!$officeId) {
    set_flash('danger', 'معرف المكتب غير صحيح');
    redirect('offices.php');
}

// Get office details
$office = $officeMgr->getOfficeById($officeId);
if (!$office) {
    set_flash('danger', 'المكتب غير موجود');
    redirect('offices.php');
}

// Get operations for this office
$operations = $officeMgr->getOfficeOperations($officeId);
if (empty($operations)) {
    set_flash('warning', 'لا توجد عمليات لهذا المكتب');
    redirect("office_details.php?id=$officeId");
}

// Get exchange rates for this office
$exchangeRates = $officeMgr->getOfficeExchangeRates($officeId);

// Calculate office balance
$balance = $officeMgr->calculateOfficeBalance($officeId);

// Calculate total base amount
$totalBaseAmount = 0;
foreach($operations as $op) {
    $opBaseAmount = isset($op['base_amount']) ? $op['base_amount'] : $op['amount'];
    if (!isset($op['base_amount']) || $op['base_amount'] == 0) {
        foreach ($exchangeRates as $rate) {
            if ($rate['operation_name'] === $op['operation_name'] && $rate['exchange_rate_percentage'] > 0) {
                $opBaseAmount = ($op['amount'] * 100) / (100 - $rate['exchange_rate_percentage']);
                break;
            }
        }
    }
    $totalBaseAmount += $opBaseAmount;
}

// Get export format
$format = strtolower($_GET['format'] ?? 'pdf');
if (!in_array($format, ['pdf', 'excel'])) {
    $format = 'pdf';
}

// Set file name
$fileName = 'office_operations_' . $officeId . '_' . date('Y-m-d');

// Process operations data
$processedOperations = [];
foreach ($operations as $operation) {
    // Use base_amount if available, otherwise calculate it
    $baseAmount = isset($operation['base_amount']) ? $operation['base_amount'] : $operation['amount'];
    $finalAmount = $operation['amount'];
    
    // If base_amount is not set (for old records), try to calculate it
    if (!isset($operation['base_amount']) || $operation['base_amount'] == 0) {
        foreach ($exchangeRates as $rate) {
            if ($rate['operation_name'] === $operation['operation_name'] && $rate['exchange_rate_percentage'] > 0) {
                $baseAmount = ($finalAmount * 100) / (100 - $rate['exchange_rate_percentage']);
                break;
            }
        }
    }
    
    $processedOperations[] = [
        'id' => $operation['id'],
        'operation_name' => $operation['operation_name'],
        'base_amount' => $baseAmount,
        'final_amount' => $finalAmount,
        'is_credit' => (string)$operation['is_credit'] === '1' ? 'لنا' : 'لكم',
        'created_at' => date('Y/m/d H:i', strtotime($operation['created_at']))
    ];
}

// Export to Excel
if ($format === 'excel') {
    // Set headers for Excel download
    header('Content-Type: application/vnd.ms-excel; charset=UTF-8');
    header('Content-Disposition: attachment; filename="' . $fileName . '.xls"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // Output Excel content
    echo '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">';
    echo '<head>';
    echo '<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">';
    echo '</head>';
    echo '<body>';
    echo '<table border="1">';
    
    // Header with logo and company info
    echo '<tr>';
    echo '<td colspan="6" style="background-color: #f0f0f0; padding: 5px 10px;">';
    echo '<table style="width: 100%; border: none;">';
    echo '<tr>';

    // Arabic info on the right
    echo '<td style="text-align: right; border: none; width: 35%; vertical-align: top; padding-right: 15px;">';
    echo '<div style="font-size: 18px; font-weight: bold; margin-bottom: 8px; color: #2c3e50;">TrustPlus</div>';
    echo '<div style="font-size: 13px; margin-bottom: 5px; color: #34495e; line-height: 1.4;">غزة - الشاطئ - بجانب العيادة العسكرية جنوباً</div>';
    echo '<div style="font-size: 13px; color: #34495e; font-weight: 500;">رقم الجوال: 0567771719</div>';
    echo '</td>';

    // Logo in the center
    echo '<td style="text-align: center; border: none; width: 30%; vertical-align: middle; padding: 5px;">';
    if (file_exists(__DIR__ . '/1.jpg')) {
        echo '<img src="1.jpg" style="width: 90px; height: 90px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: block; margin: 0 auto;" />';
    } else {
        echo '<div style="width: 90px; height: 90px; background: #3498db; border-radius: 10px; margin: 0 auto; line-height: 90px; color: white; font-weight: bold; font-size: 24px;">TP</div>';
    }
    echo '</td>';

    // English info on the left
    echo '<td style="text-align: left; border: none; width: 35%; vertical-align: top; padding-left: 15px;">';
    echo '<div style="font-size: 18px; font-weight: bold; margin-bottom: 8px; color: #2c3e50;">TrustPlus</div>';
    echo '<div style="font-size: 13px; margin-bottom: 5px; color: #34495e; line-height: 1.4;">Gaza - Beach - Next to Military Clinic South</div>';
    echo '<div style="font-size: 13px; color: #34495e; font-weight: 500;">Mobile: 0567771719</div>';
    echo '</td>';

    echo '</tr>';
    echo '</table>';
    echo '</td>';
    echo '</tr>';
    
    echo '<tr>';
    echo '<th colspan="6" style="background-color: #f0f0f0; text-align: center; font-size: 16px; font-weight: bold;">';
    echo 'تقرير عمليات المكتب - ' . htmlspecialchars($office['office_name']);
    echo '</th>';
    echo '</tr>';
    
    echo '<tr>';
    echo '<th colspan="6" style="background-color: #f0f0f0; text-align: center;">';
    echo 'تاريخ التصدير: ' . date('Y/m/d H:i');
    echo '</th>';
    echo '</tr>';
    
    // Table header
    echo '<tr style="background-color: #f0f0f0; font-weight: bold;">';
    echo '<th>الترتيب</th>';
    echo '<th>العملية</th>';
    echo '<th>المبلغ الأساسي</th>';
    echo '<th>المبلغ النهائي</th>';
    echo '<th>نوع العملية</th>';
    echo '<th>تاريخ العملية</th>';
    echo '</tr>';
    
    // Table data
    $counter = 1;
    foreach ($processedOperations as $op) {
        echo '<tr>';
        echo '<td>' . $counter . '</td>';
        echo '<td>' . htmlspecialchars($op['operation_name']) . '</td>';
        echo '<td>' . number_format($op['base_amount'], 2) . '</td>';
        echo '<td>' . number_format($op['final_amount'], 2) . '</td>';
        echo '<td>' . $op['is_credit'] . '</td>';
        echo '<td>' . $op['created_at'] . '</td>';
        echo '</tr>';
        $counter++;
    }
    
    // Table footer
    echo '<tr style="background-color: #f0f0f0; font-weight: bold;">';
    echo '<td colspan="2">المجموع:</td>';
    echo '<td>' . number_format($totalBaseAmount, 2) . '</td>';
    echo '<td>' . number_format($balance['credit_total'] + $balance['debit_total'], 2) . '</td>';
    echo '<td colspan="2"></td>';
    echo '</tr>';
    
    echo '<tr style="background-color: #f0f0f0; font-weight: bold;">';
    echo '<td colspan="2">لنا:</td>';
    echo '<td></td>';
    echo '<td>' . number_format($balance['credit_total'], 2) . '</td>';
    echo '<td colspan="2"></td>';
    echo '</tr>';
    
    echo '<tr style="background-color: #f0f0f0; font-weight: bold;">';
    echo '<td colspan="2">لكم:</td>';
    echo '<td></td>';
    echo '<td>' . number_format($balance['debit_total'], 2) . '</td>';
    echo '<td colspan="2"></td>';
    echo '</tr>';
    
    echo '<tr style="background-color: #f0f0f0; font-weight: bold;">';
    echo '<td colspan="2">الرصيد:</td>';
    echo '<td></td>';
    echo '<td>' . number_format(abs($balance['balance']), 2) . ' (' . ($balance['balance'] >= 0 ? 'لنا' : 'لكم') . ')</td>';
    echo '<td colspan="2"></td>';
    echo '</tr>';
    
    echo '</table>';
    echo '</body>';
    echo '</html>';
    
    exit;
}

// Export to PDF
if ($format === 'pdf') {
    // Check if TCPDF is installed
    if (!file_exists(__DIR__ . '/../vendor/autoload.php')) {
        set_flash('warning', 'مكتبة TCPDF غير متوفرة. يرجى تثبيت المكتبة أولاً.');
        redirect("office_details.php?id=$officeId");
    }
    
    // Include TCPDF autoloader
    require_once __DIR__ . '/../vendor/autoload.php';
    
    // Create TCPDF instance
    $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
    
    // Set document information
    $pdf->SetCreator('نظام TrustPlus');
    $pdf->SetAuthor('نظام TrustPlus');
    $pdf->SetTitle('تقرير عمليات المكتب - ' . $office['office_name']);
    $pdf->SetSubject('تقرير عمليات المكتب');
    $pdf->SetKeywords('TrustPlus, تقرير, عمليات, مكتب');
    
    // Set default header data - remove default header
    $pdf->SetHeaderData('', 0, '', '');
    
    // Set header and footer fonts
    $pdf->setHeaderFont(Array(PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN));
    $pdf->setFooterFont(Array(PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA));
    
    // Set default monospaced font
    $pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
    
    // Set margins - reduce top margin to minimize white space
    $pdf->SetMargins(PDF_MARGIN_LEFT, 10, PDF_MARGIN_RIGHT);
    $pdf->SetHeaderMargin(5);
    $pdf->SetFooterMargin(PDF_MARGIN_FOOTER);
    
    // Set auto page breaks
    $pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);
    
    // Disable header and footer
    $pdf->setPrintHeader(false);
    $pdf->setPrintFooter(false);
    
    // Set image scale factor
    $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);
    
    // Set RTL mode
    $pdf->setRTL(true);
    
    // Add a page
    $pdf->AddPage();
    
    // Set font for Arabic text
    $pdf->SetFont('aealarabiya', '', 12);
    
    // Add logo to PDF - positioned at specific coordinates above TrustPlus
    $logoPath = __DIR__ . '/1.jpg';
    if (file_exists($logoPath)) {
        try {
            // Get page width and center the logo horizontally
            $pageWidth = $pdf->getPageWidth();
            $logoWidth = 35;
            $logoHeight = 35;
            $logoX = ($pageWidth - $logoWidth) / 2 + 5; // +5 لإزاحة 5mm لليمين، -5 لإزاحة لليسار
            
            // Position logo at specific coordinates:
            // x = center of page (calculated)
            // y = 15mm from top (reduced to minimize white space)
            // width = 35mm, height = 35mm
            $pdf->Image($logoPath, $logoX, 15, $logoWidth, $logoHeight, 'JPG', '', '', true, 300, '', false, false, 0, false, false, false);
        } catch (Exception $e) {
            // If image fails, just continue without logo
            error_log('TCPDF Image Error: ' . $e->getMessage());
        }
    }
    
    // Build HTML content
    $html = '
    <style>
        body {
            font-family: aealarabiya;
            direction: rtl;
            text-align: right;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            font-size: 18px;
            margin: 0;
            padding: 0;
            font-weight: bold;
        }
        .header p {
            font-size: 12px;
            margin: 5px 0;
            padding: 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .footer {
            text-align: center;
            font-size: 10px;
            margin-top: 20px;
        }
        .summary {
            margin-top: 20px;
        }
        .summary table {
            width: 60%;
            margin: 0 auto;
        }
        .credit {
            color: #008000;
        }
        .debit {
            color: #FF0000;
        }
        .balance {
            font-weight: bold;
        }
        .company-header {
            width: 100%;
            margin-top: 5px;
            margin-bottom: 15px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .company-info {
            width: 100%;
            border-collapse: collapse;
        }
        .company-info td {
            border: none;
            vertical-align: top;
            padding: 5px;
        }
        .arabic-info {
            text-align: right;
            width: 35%;
        }
        .logo-center {
            text-align: center;
            width: 30%;
            vertical-align: middle;
            position: relative;
        }
        .english-info {
            text-align: left;
            width: 35%;
        }
        .company-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        .company-address {
            font-size: 12px;
            margin-bottom: 3px;
            color: #666;
        }
        .company-phone {
            font-size: 12px;
            color: #666;
        }
    </style>
    
    <div class="header">
        <div class="company-header">
            <table class="company-info">
                <tr>
                    <td class="arabic-info">
                        <div class="company-name">TrustPlus</div>
                        <div class="company-address">غزة - الشاطئ - بجانب العيادة العسكرية جنوباً</div>
                        <div class="company-phone">رقم الجوال: 0567771719</div>
                    </td>
                    <td class="logo-center">
                        <!-- Logo space - handled by TCPDF Image function -->
                    </td>
                    <td class="english-info">
                        <div class="company-name">TrustPlus</div>
                        <div class="company-address">Gaza - Beach - Next to Military Clinic South</div>
                        <div class="company-phone">Mobile: 0567771719</div>
                    </td>
                </tr>
            </table>
        </div>
        <h1>تقرير عمليات المكتب - ' . htmlspecialchars($office['office_name']) . '</h1>
        <p>تاريخ التصدير: ' . date('Y/m/d H:i') . '</p>
        <p>مسؤول المكتب: ' . htmlspecialchars($office['manager_name']) . '</p>
        <p>رقم الهاتف: ' . htmlspecialchars($office['phone_number']) . '</p>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>الترتيب</th>
                <th>العملية</th>
                <th>المبلغ الأساسي</th>
                <th>المبلغ النهائي</th>
                <th>نوع العملية</th>
                <th>تاريخ العملية</th>
            </tr>
        </thead>
        <tbody>';
    
    $counter = 1;
    foreach ($processedOperations as $op) {
        $html .= '
            <tr>
                <td>' . $counter . '</td>
                <td>' . htmlspecialchars($op['operation_name']) . '</td>
                <td>' . number_format($op['base_amount'], 2) . '</td>
                <td>' . number_format($op['final_amount'], 2) . '</td>
                <td>' . $op['is_credit'] . '</td>
                <td>' . $op['created_at'] . '</td>
            </tr>';
        $counter++;
    }
    
    $html .= '
        </tbody>
        <tfoot>
            <tr>
                <th colspan="2">المجموع:</th>
                <th>' . number_format($totalBaseAmount, 2) . '</th>
                <th>' . number_format($balance['credit_total'] + $balance['debit_total'], 2) . '</th>
                <th colspan="2"></th>
            </tr>
        </tfoot>
    </table>
    
    <div class="summary">
        <table>
            <tr>
                <th>لنا:</th>
                <td class="credit">' . number_format($balance['credit_total'], 2) . '</td>
            </tr>
            <tr>
                <th>لكم:</th>
                <td class="debit">' . number_format($balance['debit_total'], 2) . '</td>
            </tr>
            <tr>
                <th>الرصيد:</th>
                <td class="balance ' . ($balance['balance'] >= 0 ? 'credit' : 'debit') . '">' . 
                    number_format(abs($balance['balance']), 2) . ' (' . 
                    ($balance['balance'] >= 0 ? 'لنا' : 'لكم') . ')' . 
                '</td>
            </tr>
        </table>
    </div>
    
    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام TrustPlus</p>
        <p>© ' . date('Y') . ' جميع الحقوق محفوظة</p>
    </div>';
    
    // Write HTML to PDF
    $pdf->writeHTML($html, true, false, true, false, '');
    
    // Output PDF
    $pdf->Output($fileName . '.pdf', 'I');
    exit;
}

// If we reach here, redirect back to office details
redirect("office_details.php?id=$officeId");
?> 